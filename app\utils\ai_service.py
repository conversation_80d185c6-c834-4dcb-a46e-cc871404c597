import requests
import json
from flask import current_app
from app.models.message import Message
from app.models.conversation import Conversation

def get_conversation_history(conversation_id, limit=10):
    """获取对话历史，用于提供上下文"""
    try:
        messages = Message.query.filter_by(conversation_id=conversation_id)\
            .order_by(Message.created_at.desc())\
            .limit(limit)\
            .all()
        
        # 反转顺序，使其按时间正序排列
        messages.reverse()
        
        history = []
        for msg in messages:
            history.append({
                'role': msg.role,
                'content': msg.content
            })
        
        return history
    except Exception as e:
        current_app.logger.error(f"获取对话历史失败: {e}")
        return []

def get_ai_response(user_message, conversation_id=None):
    """
    获取AI回复
    这里提供了一个基础的实现框架，可以根据需要集成不同的AI服务
    """
    try:
        # 获取配置
        ai_api_key = current_app.config.get('AI_API_KEY')
        ai_api_url = current_app.config.get('AI_API_URL')

        current_app.logger.info(f"AI配置检查 - API Key: {'已配置' if ai_api_key else '未配置'}, API URL: {ai_api_url}")

        # 如果没有配置AI服务，返回模拟回复
        if not ai_api_key or not ai_api_url:
            current_app.logger.info("AI服务未配置，使用模拟回复")
            return get_mock_medical_response(user_message)
        
        # 构建请求数据
        messages = []
        
        # 添加系统提示
        messages.append({
            'role': 'system',
            'content': '''你是一个专业的AI医疗助手。请注意：
1. 你只能提供一般性的健康信息和建议
2. 不能替代专业医生的诊断和治疗
3. 对于严重症状，建议用户及时就医
4. 回答要专业、准确、易懂
5. 如果不确定，请明确说明并建议咨询医生'''
        })
        
        # 添加对话历史（如果有）
        if conversation_id:
            history = get_conversation_history(conversation_id)
            messages.extend(history)
        
        # 添加当前用户消息
        messages.append({
            'role': 'user',
            'content': user_message
        })
        
        # 调用AI API（这里以OpenAI格式为例）
        headers = {
            'Authorization': f'Bearer {ai_api_key}',
            'Content-Type': 'application/json'
        }
        
        # 根据API URL判断使用的模型
        if 'deepseek' in ai_api_url.lower():
            model_name = 'deepseek-chat'
        else:
            model_name = 'gpt-3.5-turbo'

        data = {
            'model': model_name,
            'messages': messages,
            'max_tokens': 1000,
            'temperature': 0.7
        }
        
        response = requests.post(
            ai_api_url,
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            ai_reply = result['choices'][0]['message']['content']
            return ai_reply.strip()
        else:
            current_app.logger.error(f"AI API错误: {response.status_code} - {response.text}")
            return get_mock_medical_response(user_message)
            
    except requests.exceptions.Timeout:
        current_app.logger.error("AI API请求超时")
        return "抱歉，AI服务响应超时，请稍后重试。"
    except requests.exceptions.RequestException as e:
        current_app.logger.error(f"AI API请求失败: {e}")
        return get_mock_medical_response(user_message)
    except Exception as e:
        current_app.logger.error(f"AI服务异常: {e}")
        return "抱歉，AI服务暂时不可用，请稍后重试。"

def get_mock_medical_response(user_message):
    """
    模拟医疗AI回复，用于测试和演示
    在没有配置真实AI服务时使用
    """
    message_lower = user_message.lower()
    
    # 简单的关键词匹配回复
    if any(keyword in message_lower for keyword in ['头痛', '头疼', '头晕']):
        return """关于头痛症状，可能的原因包括：

1. **常见原因**：
   - 紧张性头痛（压力、疲劳）
   - 偏头痛
   - 睡眠不足
   - 脱水

2. **建议措施**：
   - 保证充足睡眠
   - 多喝水
   - 适当休息
   - 避免过度用眼

3. **需要就医的情况**：
   - 头痛持续加重
   - 伴有发热、呕吐
   - 突然剧烈头痛
   - 头痛伴有视力模糊

**重要提醒**：如果症状持续或加重，请及时就医咨询专业医生。"""

    elif any(keyword in message_lower for keyword in ['发烧', '发热', '体温']):
        return """关于发热症状：

1. **体温参考**：
   - 正常体温：36-37°C
   - 低热：37.1-38°C
   - 中等热：38.1-39°C
   - 高热：39.1-41°C

2. **处理建议**：
   - 多喝温水
   - 适当休息
   - 物理降温（温水擦拭）
   - 监测体温变化

3. **立即就医情况**：
   - 体温超过39°C
   - 伴有呼吸困难
   - 持续高热不退
   - 婴幼儿发热

**重要提醒**：发热可能是多种疾病的症状，建议及时就医明确病因。"""

    elif any(keyword in message_lower for keyword in ['咳嗽', '咳痰']):
        return """关于咳嗽症状：

1. **常见类型**：
   - 干咳：无痰或少痰
   - 湿咳：有痰液
   - 急性咳嗽：持续<3周
   - 慢性咳嗽：持续>8周

2. **一般护理**：
   - 多喝温水
   - 保持室内湿度
   - 避免刺激性气味
   - 适当休息

3. **需要关注**：
   - 咳血
   - 持续高热
   - 呼吸困难
   - 胸痛

**重要提醒**：持续咳嗽或伴有其他症状时，请及时就医检查。"""

    else:
        return """感谢您的咨询。作为AI医疗助手，我可以为您提供一般性的健康信息。

**请注意**：
- 我的建议仅供参考，不能替代专业医疗诊断
- 如有身体不适，建议及时就医
- 紧急情况请立即拨打急救电话

**我可以帮助您了解**：
- 常见症状的一般信息
- 基础的健康知识
- 预防保健建议
- 就医指导

请详细描述您的症状或健康问题，我会尽力为您提供有用的信息。"""
